<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="madhat_sitemap_urls" resource="default" engine="innodb" comment="MadHat Sitemap URLs Table">
        <column xsi:type="int" name="id" unsigned="true" nullable="false" identity="true" comment="ID"/>
        <column xsi:type="varchar" name="url" nullable="false" length="500" comment="URL"/>
        <column xsi:type="varchar" name="url_type" nullable="false" length="50" comment="URL Type (missing_indexable, non_indexable)"/>
        <column xsi:type="varchar" name="status_code" nullable="true" length="10" comment="HTTP Status Code"/>
        <column xsi:type="varchar" name="indexability" nullable="true" length="50" comment="Indexability Status"/>
        <column xsi:type="varchar" name="canonical_url" nullable="true" length="500" comment="Canonical URL"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP" comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP" comment="Updated At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="id"/>
        </constraint>
        <index referenceId="MADHAT_SITEMAP_URLS_URL" indexType="btree">
            <column name="url"/>
        </index>
        <index referenceId="MADHAT_SITEMAP_URLS_URL_TYPE" indexType="btree">
            <column name="url_type"/>
        </index>
    </table>
</schema>
