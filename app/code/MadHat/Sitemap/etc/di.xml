<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Sitemap\Model\ItemProvider\Product">
        <plugin name="modify_sitemap_product_urls" type="MadHat\Sitemap\Plugin\Sitemap\ProductItemProviderPlugin"/>
    </type>

    <!-- Register custom URL item provider for sitemap -->
    <type name="Magento\Sitemap\Model\ItemProvider\Composite">
        <arguments>
            <argument name="itemProviders" xsi:type="array">
                <item name="customUrls" xsi:type="object">MadHat\Sitemap\Model\ItemProvider\CustomUrl</item>
            </argument>
        </arguments>
    </type>

    <!-- Data provider for grid -->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="madhat_sitemap_urls_listing_data_source" xsi:type="string">MadHat\Sitemap\Model\ResourceModel\SitemapUrl\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <!-- Virtual type for grid collection -->
    <virtualType name="MadHat\Sitemap\Model\ResourceModel\SitemapUrl\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">madhat_sitemap_urls</argument>
            <argument name="resourceModel" xsi:type="string">MadHat\Sitemap\Model\ResourceModel\SitemapUrl</argument>
        </arguments>
    </virtualType>
</config>
