<?php
/**
 * Copyright © MadHat, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace MadHat\Sitemap\Block\Adminhtml\SitemapUrl\Edit;

use Magento\Backend\Block\Widget\Context;
use Magento\Framework\Registry;

/**
 * Class GenericButton
 * 
 * Generic button for sitemap URL form
 */
class GenericButton
{
    /**
     * @var Context
     */
    protected $context;

    /**
     * @var Registry
     */
    protected $registry;

    /**
     * @param Context $context
     * @param Registry $registry
     */
    public function __construct(
        Context $context,
        Registry $registry
    ) {
        $this->context = $context;
        $this->registry = $registry;
    }

    /**
     * Return model ID
     *
     * @return int|null
     */
    public function getModelId()
    {
        $sitemapUrl = $this->registry->registry('madhat_sitemap_url');
        return $sitemapUrl ? $sitemapUrl->getId() : null;
    }

    /**
     * Generate url by route and parameters
     *
     * @param   string $route
     * @param   array $params
     * @return  string
     */
    public function getUrl($route = '', $params = [])
    {
        return $this->context->getUrlBuilder()->getUrl($route, $params);
    }
}
