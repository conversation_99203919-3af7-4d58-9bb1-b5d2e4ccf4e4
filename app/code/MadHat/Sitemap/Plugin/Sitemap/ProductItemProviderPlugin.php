<?php

namespace MadHat\Sitemap\Plugin\Sitemap;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Sitemap\Model\ItemProvider\Product as Subject;
use Magento\Sitemap\Model\SitemapItemInterfaceFactory;
use MadHat\Catalog\Model\BrandAttributeOptionsProvider;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

class ProductItemProviderPlugin
{
    private $sitemapItemFactory;
    private $brandAttributeOptionsProvider;
    private $logger;
    private $productRepository;
    private $resourceConnection;
    private $nonIndexableUrls = [];
    private $nonIndexableUrlsLoaded = false;

    public function __construct(
        BrandAttributeOptionsProvider $brandAttributeOptionsProvider,
        SitemapItemInterfaceFactory $sitemapItemFactory,
        LoggerInterface $logger,
        ProductRepositoryInterface $productRepository,
        ResourceConnection $resourceConnection
    ) {
        $this->brandAttributeOptionsProvider = $brandAttributeOptionsProvider;
        $this->sitemapItemFactory = $sitemapItemFactory;
        $this->logger = $logger;
        $this->productRepository = $productRepository;
        $this->resourceConnection = $resourceConnection;
    }

    /**
     * Modify product URLs in sitemap items.
     *
     * @param Subject $subject
     * @param array $result
     * @param int $storeId
     * @return array
     */
    public function afterGetItems(Subject $subject, array $result, $storeId)
    {
        $modifiedItems = [];

        foreach ($result as $item) {
            // Get the original URL
            $originalUrl = $item->getUrl();

            // Skip URLs that should not be in sitemap
            if ($this->shouldExcludeUrl($originalUrl)) {
                $this->logger->debug('Excluding URL from sitemap: ' . $originalUrl);
                continue;
            }

            // Modify the URL as per your requirements
            $modifiedUrl = $this->modifyProductUrl($originalUrl);

            // Skip if modified URL is invalid
            if ($this->shouldExcludeUrl($modifiedUrl)) {
                $this->logger->debug('Excluding modified URL from sitemap: ' . $modifiedUrl);
                continue;
            }

            // Create a new sitemap item with the modified URL
            $modifiedItem = $this->sitemapItemFactory->create([
                'url'             => $modifiedUrl,
                'updatedAt'       => $item->getUpdatedAt(),
                'images'          => $item->getImages(),
                'priority'        => $item->getPriority(),
                'changeFrequency' => $item->getChangeFrequency(),
            ]);

            $modifiedItems[] = $modifiedItem;
        }

        return $modifiedItems;
    }

    /**
     * Check if URL should be excluded from sitemap.
     *
     * @param string $url
     * @return bool
     */
    private function shouldExcludeUrl(string $url): bool
    {
        // Load non-indexable URLs if not already loaded
        $this->loadNonIndexableUrls();

        // Check against non-indexable URLs from database
        foreach ($this->nonIndexableUrls as $nonIndexableData) {
            if ($url === $nonIndexableData['url']) {
                return true;
            }
        }

        // Exclude URLs with spaces or URL-encoded characters
        if (strpos($url, ' ') !== false || strpos($url, '%20') !== false || strpos($url, '%') !== false) {
            return true;
        }

        // Exclude non-canonical URLs (direct product view URLs)
        if (strpos($url, '/catalog/product/view/id/') !== false) {
            return true;
        }

        // Exclude B2B URLs
        if (strpos($url, '/b2b/') !== false) {
            return true;
        }

        // Exclude empty or invalid URLs
        if (empty($url) || $url === '/') {
            return true;
        }

        return false;
    }

    /**
     * Custom function to modify the product URL.
     *
     * @param string $url
     * @return string
     */
    private function modifyProductUrl(string $url): string
    {
        $modifiedUrl = $url;

        // Log the original URL
        $this->logger->debug('Original URL: ' . $url);

        // Extract SKU from URL - improved extraction method
        $sku = $this->extractSkuFromUrl($url);
        if (!$sku) {
            $this->logger->debug('Could not extract SKU from URL: ' . $url);
            return $modifiedUrl;
        }

        $this->logger->debug('Extracted SKU: ' . $sku);

        try {
            // Load product by SKU
            $product = $this->productRepository->get($sku);

            // Retrieve brand attribute
            $brand = $product->getAttributeText('madhat_brand');
            if (!$brand || strtolower($brand) === 'no brand' || strtolower($brand) === 'no value') {
                $this->logger->debug('No valid brand for SKU ' . $sku);
                return $modifiedUrl;
            }

            $this->logger->debug('Brand for SKU ' . $sku . ': ' . $brand);

            // Get brand URL key
            $brandUrlKey = $this->getBrandUrlKey($brand);
            if ($brandUrlKey) {
                $modifiedUrl = $brandUrlKey . '/' . $url;
                $this->logger->debug('Modified URL with brand: ' . $modifiedUrl);
            }

        } catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
            $this->logger->debug('No product found for SKU: ' . $sku);
        } catch (\Exception $e) {
            $this->logger->error('Error loading product for SKU ' . $sku . ': ' . $e->getMessage());
        }

        return $modifiedUrl;
    }

    /**
     * Extract SKU from URL.
     *
     * @param string $url
     * @return string|null
     */
    private function extractSkuFromUrl(string $url): ?string
    {
        // Remove leading slash if present
        $url = ltrim($url, '/');

        // Split by dash and get the last part (should be SKU)
        $urlParts = explode('-', $url);
        $potentialSku = end($urlParts);

        // Basic validation - SKU should be numeric for this system
        if (is_numeric($potentialSku)) {
            return $potentialSku;
        }

        return null;
    }

    /**
     * Get brand URL key from brand name.
     *
     * @param string $brand
     * @return string|null
     */
    private function getBrandUrlKey(string $brand): ?string
    {
        try {
            $prepareOptions = $this->brandAttributeOptionsProvider->getMadhatBrandAttributeOptions();
            $brandKey = strtolower(str_replace(' ', '-', trim($brand)));

            if (isset($prepareOptions[$brandKey])) {
                return $brandKey;
            }

            // Fallback: use the static method from BrandAttributeOptionsProvider
            return \MadHat\Catalog\Model\BrandAttributeOptionsProvider::getMadhatBrandNameForUrl($brand);

        } catch (\Exception $e) {
            $this->logger->error('Error getting brand URL key for brand ' . $brand . ': ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Load non-indexable URLs from database
     */
    private function loadNonIndexableUrls(): void
    {
        if ($this->nonIndexableUrlsLoaded) {
            return;
        }

        try {
            $connection = $this->resourceConnection->getConnection();
            $urlTable = $this->resourceConnection->getTableName('madhat_sitemap_urls');

            $this->nonIndexableUrls = $connection->fetchAll(
                $connection->select()
                    ->from($urlTable, ['url', 'canonical_url'])
                    ->where('url_type = ?', 'non_indexable')
            );

            $this->logger->info('Loaded ' . count($this->nonIndexableUrls) . ' non-indexable URLs for filtering');
            $this->nonIndexableUrlsLoaded = true;
        } catch (\Exception $e) {
            $this->logger->error('Error loading non-indexable URLs from database: ' . $e->getMessage());
        }
    }

}
