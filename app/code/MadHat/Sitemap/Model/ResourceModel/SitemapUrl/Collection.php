<?php
/**
 * Copyright © MadHat, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace MadHat\Sitemap\Model\ResourceModel\SitemapUrl;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;
use MadHat\Sitemap\Model\SitemapUrl;
use MadHat\Sitemap\Model\ResourceModel\SitemapUrl as SitemapUrlResource;

/**
 * Class Collection
 * 
 * Collection for sitemap URLs
 */
class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'id';

    /**
     * @inheritdoc
     */
    protected function _construct()
    {
        $this->_init(SitemapUrl::class, SitemapUrlResource::class);
    }
}
