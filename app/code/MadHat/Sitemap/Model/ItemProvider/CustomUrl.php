<?php

namespace MadHat\Sitemap\Model\ItemProvider;

use Magento\Sitemap\Model\ItemProvider\ItemProviderInterface;
use Magento\Sitemap\Model\SitemapItemInterfaceFactory;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;

/**
 * Class CustomUrl
 *
 * Custom item provider for sitemap that:
 * 1. Adds missing indexable pages from CSV file
 * 2. Provides canonical URLs to replace non-indexable ones
 */
class CustomUrl implements ItemProviderInterface
{
    private SitemapItemInterfaceFactory $sitemapItemFactory;
    private ResourceConnection $resourceConnection;
    private LoggerInterface $logger;

    /**
     * @param SitemapItemInterfaceFactory $sitemapItemFactory
     * @param ResourceConnection $resourceConnection
     * @param LoggerInterface $logger
     */
    public function __construct(
        SitemapItemInterfaceFactory $sitemapItemFactory,
        ResourceConnection $resourceConnection,
        LoggerInterface $logger
    ) {
        $this->sitemapItemFactory = $sitemapItemFactory;
        $this->resourceConnection = $resourceConnection;
        $this->logger = $logger;
    }

    /**
     * Get sitemap items
     *
     * @param int $storeId
     * @return array
     */
    public function getItems($storeId)
    {
        $items = [];
        $connection = $this->resourceConnection->getConnection();
        $urlTable = $this->resourceConnection->getTableName('madhat_sitemap_urls');

        try {
            // Add missing indexable URLs
            $missingUrls = $connection->fetchAll(
                $connection->select()
                    ->from($urlTable, ['url'])
                    ->where('url_type = ?', 'missing_indexable')
            );

            foreach ($missingUrls as $urlData) {
                $items[] = $this->sitemapItemFactory->create([
                    'url' => $urlData['url'],
                    'updatedAt' => date('Y-m-d H:i:s'),
                    'images' => [],
                    'priority' => '0.5',
                    'changeFrequency' => 'weekly',
                ]);
                $this->logger->info("Added missing indexable URL to sitemap: {$urlData['url']}");
            }

            // Add canonical URLs for non-indexable pages
            $canonicalUrls = $connection->fetchAll(
                $connection->select()
                    ->from($urlTable, ['canonical_url'])
                    ->where('url_type = ?', 'non_indexable')
                    ->where('canonical_url IS NOT NULL')
                    ->where('canonical_url != ?', '')
            );

            foreach ($canonicalUrls as $urlData) {
                if ($urlData['canonical_url']) {
                    $items[] = $this->sitemapItemFactory->create([
                        'url' => $urlData['canonical_url'],
                        'updatedAt' => date('Y-m-d H:i:s'),
                        'images' => [],
                        'priority' => '0.5',
                        'changeFrequency' => 'weekly',
                    ]);
                    $this->logger->info("Added canonical URL to sitemap: {$urlData['canonical_url']}");
                }
            }

        } catch (\Exception $e) {
            $this->logger->error('Error loading URLs from database: ' . $e->getMessage());
        }

        return $items;
    }

}
