<?xml version="1.0"?>
<!--
/**
 * Copyright © MadHat, Inc. All rights reserved.
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">madhat_sitemap_url_form.madhat_sitemap_url_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">Sitemap URL Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>
    <settings>
        <buttons>
            <button name="save" class="MadHat\Sitemap\Block\Adminhtml\SitemapUrl\Edit\SaveButton"/>
            <button name="delete" class="MadHat\Sitemap\Block\Adminhtml\SitemapUrl\Edit\DeleteButton"/>
            <button name="back" class="MadHat\Sitemap\Block\Adminhtml\SitemapUrl\Edit\BackButton"/>
        </buttons>
        <namespace>madhat_sitemap_url_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>madhat_sitemap_url_form.madhat_sitemap_url_form_data_source</dep>
        </deps>
    </settings>
    <dataSource name="madhat_sitemap_url_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
        <settings>
            <submitUrl path="madhat_sitemap/urls/save"/>
        </settings>
        <dataProvider class="MadHat\Sitemap\Ui\DataProvider\SitemapUrl\Form\SitemapUrlDataProvider" name="madhat_sitemap_url_form_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <fieldset name="general">
        <settings>
            <label translate="true">General Information</label>
        </settings>
        <field name="id" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">sitemap_url</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <visible>false</visible>
                <dataScope>id</dataScope>
            </settings>
        </field>
        <field name="url" sortOrder="10" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">sitemap_url</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">URL</label>
                <dataScope>url</dataScope>
            </settings>
        </field>
        <field name="url_type" sortOrder="20" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">sitemap_url</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">URL Type</label>
                <dataScope>url_type</dataScope>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="MadHat\Sitemap\Ui\Component\Listing\Column\UrlType\Options"/>
                        <caption translate="true">-- Please Select --</caption>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="status_code" sortOrder="30" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">sitemap_url</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Status Code</label>
                <dataScope>status_code</dataScope>
            </settings>
        </field>
        <field name="indexability" sortOrder="40" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">sitemap_url</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Indexability</label>
                <dataScope>indexability</dataScope>
            </settings>
        </field>
        <field name="canonical_url" sortOrder="50" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">sitemap_url</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Canonical URL</label>
                <dataScope>canonical_url</dataScope>
            </settings>
        </field>
    </fieldset>
</form>
