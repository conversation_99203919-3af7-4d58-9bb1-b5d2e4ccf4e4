<?xml version="1.0"?>
<!--
/**
 * Copyright © MadHat, Inc. All rights reserved.
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">madhat_sitemap_urls_listing.madhat_sitemap_urls_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <buttons>
            <button name="add">
                <url path="*/*/new"/>
                <class>primary</class>
                <label translate="true">Add New URL</label>
            </button>
        </buttons>
        <spinner>madhat_sitemap_urls_columns</spinner>
        <deps>
            <dep>madhat_sitemap_urls_listing.madhat_sitemap_urls_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="madhat_sitemap_urls_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>MadHat_Sitemap::sitemap_urls</aclResource>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider" name="madhat_sitemap_urls_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters">
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
            <filterSelect name="url_type" provider="${ $.parentName }">
                <settings>
                    <captionValue>0</captionValue>
                    <options class="MadHat\Sitemap\Ui\Component\Listing\Column\UrlType\Options"/>
                    <caption translate="true">Select...</caption>
                    <label translate="true">URL Type</label>
                    <dataScope>url_type</dataScope>
                    <imports>
                        <link name="visible">componentType = column, index = ${ $.index }:visible</link>
                    </imports>
                </settings>
            </filterSelect>
        </filters>
        <massaction name="listing_massaction">
            <action name="delete">
                <settings>
                    <confirm>
                        <message translate="true">Are you sure you want to delete selected items?</message>
                        <title translate="true">Delete items</title>
                    </confirm>
                    <url path="madhat_sitemap/urls/massDelete"/>
                    <type>delete</type>
                    <label translate="true">Delete</label>
                </settings>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="madhat_sitemap_urls_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="madhat_sitemap/urls/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">madhat_sitemap_urls_listing.madhat_sitemap_urls_listing.madhat_sitemap_urls_columns.ids</param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">madhat_sitemap_urls_listing.madhat_sitemap_urls_listing.madhat_sitemap_urls_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>id</indexField>
            </settings>
        </selectionsColumn>
        <column name="id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="url">
            <settings>
                <filter>text</filter>
                <editor>
                    <validation>
                        <rule name="required-entry" xsi:type="boolean">true</rule>
                    </validation>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">URL</label>
            </settings>
        </column>
        <column name="url_type" component="Magento_Ui/js/grid/columns/select">
            <settings>
                <options class="MadHat\Sitemap\Ui\Component\Listing\Column\UrlType\Options"/>
                <filter>select</filter>
                <editor>
                    <editorType>select</editorType>
                </editor>
                <dataType>select</dataType>
                <label translate="true">URL Type</label>
            </settings>
        </column>
        <column name="status_code">
            <settings>
                <filter>text</filter>
                <editor>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">Status Code</label>
            </settings>
        </column>
        <column name="indexability">
            <settings>
                <filter>text</filter>
                <editor>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">Indexability</label>
            </settings>
        </column>
        <column name="canonical_url">
            <settings>
                <filter>text</filter>
                <editor>
                    <editorType>text</editorType>
                </editor>
                <label translate="true">Canonical URL</label>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created At</label>
            </settings>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Updated At</label>
            </settings>
        </column>
        <actionsColumn name="actions" class="MadHat\Sitemap\Ui\Component\Listing\Column\Actions">
            <settings>
                <indexField>id</indexField>
            </settings>
        </actionsColumn>
    </columns>
</listing>
