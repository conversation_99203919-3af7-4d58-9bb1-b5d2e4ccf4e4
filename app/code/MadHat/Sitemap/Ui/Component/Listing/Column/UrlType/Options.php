<?php
/**
 * Copyright © MadHat, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace MadHat\Sitemap\Ui\Component\Listing\Column\UrlType;

use Magento\Framework\Data\OptionSourceInterface;
use MadHat\Sitemap\Model\SitemapUrl;

/**
 * Class Options
 * 
 * Options for URL type field
 */
class Options implements OptionSourceInterface
{
    /**
     * @var SitemapUrl
     */
    protected $sitemapUrl;

    /**
     * @param SitemapUrl $sitemapUrl
     */
    public function __construct(SitemapUrl $sitemapUrl)
    {
        $this->sitemapUrl = $sitemapUrl;
    }

    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        $options = [];
        foreach ($this->sitemapUrl->getAvailableUrlTypes() as $key => $value) {
            $options[] = [
                'label' => $value,
                'value' => $key
            ];
        }
        return $options;
    }
}
