<?php
/**
 * Copyright © MadHat, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace MadHat\Sitemap\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchVersionInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\DB\Ddl\Table;
use Psr\Log\LoggerInterface;

/**
 * Class ImportSitemapUrls
 * 
 * This patch imports URL data from CSV files into the madhat_sitemap_urls database table.
 */
class ImportSitemapUrls implements DataPatchInterface, PatchVersionInterface
{
    private ModuleDataSetupInterface $moduleDataSetup;
    private File $fileDriver;
    private DirectoryList $directoryList;
    private LoggerInterface $logger;

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param File $fileDriver
     * @param DirectoryList $directoryList
     * @param LoggerInterface $logger
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        File $fileDriver,
        DirectoryList $directoryList,
        LoggerInterface $logger
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->fileDriver = $fileDriver;
        $this->directoryList = $directoryList;
        $this->logger = $logger;
    }

    /**
     * {@inheritdoc}
     */
    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        try {
            $connection = $this->moduleDataSetup->getConnection();
            
            // 1. Ensure table exists with proper structure
            $this->ensureTableExists($connection);
            
            // 2. Import URL data from CSV files
            $this->importUrlData($connection);

        } catch (\Exception $e) {
            $this->logger->error('Error in ImportSitemapUrls patch: ' . $e->getMessage());
            throw $e;
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * Ensure the madhat_sitemap_urls table exists with proper structure
     *
     * @param \Magento\Framework\DB\Adapter\AdapterInterface $connection
     */
    private function ensureTableExists($connection): void
    {
        $tableName = $this->moduleDataSetup->getTable('madhat_sitemap_urls');

        // If table exists, drop it to recreate with proper structure
        if ($connection->isTableExists($tableName)) {
            $this->logger->info('Dropping existing madhat_sitemap_urls table to recreate with proper structure');
            $connection->dropTable($tableName);
        }

        // Create table with proper structure
        $table = $connection->newTable($tableName)
            ->addColumn(
                'id',
                Table::TYPE_INTEGER,
                null,
                ['identity' => true, 'nullable' => false, 'primary' => true, 'unsigned' => true],
                'ID'
            )
            ->addColumn(
                'url',
                Table::TYPE_TEXT,
                500,
                ['nullable' => false],
                'URL'
            )
            ->addColumn(
                'url_type',
                Table::TYPE_TEXT,
                50,
                ['nullable' => false],
                'URL Type (missing_indexable, non_indexable)'
            )
            ->addColumn(
                'status_code',
                Table::TYPE_TEXT,
                10,
                ['nullable' => true],
                'HTTP Status Code'
            )
            ->addColumn(
                'indexability',
                Table::TYPE_TEXT,
                50,
                ['nullable' => true],
                'Indexability Status'
            )
            ->addColumn(
                'canonical_url',
                Table::TYPE_TEXT,
                500,
                ['nullable' => true],
                'Canonical URL'
            )
            ->addColumn(
                'created_at',
                Table::TYPE_TIMESTAMP,
                null,
                ['nullable' => false, 'default' => Table::TIMESTAMP_INIT],
                'Created At'
            )
            ->addColumn(
                'updated_at',
                Table::TYPE_TIMESTAMP,
                null,
                ['nullable' => false, 'default' => Table::TIMESTAMP_INIT_UPDATE],
                'Updated At'
            )
            ->addIndex(
                $connection->getIndexName($tableName, ['url']),
                ['url']
            )
            ->addIndex(
                $connection->getIndexName($tableName, ['url_type']),
                ['url_type']
            )
            ->setComment('MadHat Sitemap URLs Table');

        $connection->createTable($table);
        $this->logger->info('Created madhat_sitemap_urls table with proper structure');
    }

    /**
     * Import URL data from CSV files into database
     *
     * @param \Magento\Framework\DB\Adapter\AdapterInterface $connection
     */
    private function importUrlData($connection): void
    {
        $urlTable = $this->moduleDataSetup->getTable('madhat_sitemap_urls');
        
        try {
            // Import missing indexable URLs
            $missingUrlsFile = __DIR__ . '/urls_missing_in_sitemap_2025-08-26.csv';
            $this->logger->info('Checking for missing URLs file: ' . $missingUrlsFile);
            
            if ($this->fileDriver->isExists($missingUrlsFile)) {
                $this->logger->info('Found missing URLs file, parsing...');
                $missingUrls = $this->parseCsvFile($missingUrlsFile);
                $this->logger->info('Parsed ' . count($missingUrls) . ' missing URLs from CSV');
                $this->importMissingUrls($connection, $urlTable, $missingUrls);
            } else {
                $this->logger->warning('Missing URLs file not found: ' . $missingUrlsFile);
            }

            // Import non-indexable URLs
            $nonIndexableUrlsFile = __DIR__ . '/nonindexable_urls_in_sitemap_2025-08-26.csv';
            $this->logger->info('Checking for non-indexable URLs file: ' . $nonIndexableUrlsFile);
            
            if ($this->fileDriver->isExists($nonIndexableUrlsFile)) {
                $this->logger->info('Found non-indexable URLs file, parsing...');
                $nonIndexableUrls = $this->parseCsvFile($nonIndexableUrlsFile);
                $this->logger->info('Parsed ' . count($nonIndexableUrls) . ' non-indexable URLs from CSV');
                $this->importNonIndexableUrls($connection, $urlTable, $nonIndexableUrls);
            } else {
                $this->logger->warning('Non-indexable URLs file not found: ' . $nonIndexableUrlsFile);
            }

            // Check final table count
            $totalCount = $connection->fetchOne("SELECT COUNT(*) FROM {$urlTable}");
            $this->logger->info('Total URLs imported into table: ' . $totalCount);

        } catch (\Exception $e) {
            $this->logger->error('Error importing URL data: ' . $e->getMessage());
        }
    }

    /**
     * Import missing indexable URLs
     *
     * @param \Magento\Framework\DB\Adapter\AdapterInterface $connection
     * @param string $urlTable
     * @param array $missingUrls
     */
    private function importMissingUrls($connection, string $urlTable, array $missingUrls): void
    {
        $insertData = [];
        
        foreach ($missingUrls as $urlData) {
            $url = $this->extractRelativeUrl($urlData['Address'] ?? '');
            if ($url && $this->isIndexable($urlData)) {
                $insertData[] = [
                    'url' => $url,
                    'url_type' => 'missing_indexable',
                    'status_code' => $urlData['Status Code'] ?? null,
                    'indexability' => $urlData['Indexability'] ?? null,
                    'canonical_url' => null
                ];
            }
        }

        if (!empty($insertData)) {
            $connection->insertMultiple($urlTable, $insertData);
            $this->logger->info('Imported ' . count($insertData) . ' missing indexable URLs');
        }
    }

    /**
     * Import non-indexable URLs
     *
     * @param \Magento\Framework\DB\Adapter\AdapterInterface $connection
     * @param string $urlTable
     * @param array $nonIndexableUrls
     */
    private function importNonIndexableUrls($connection, string $urlTable, array $nonIndexableUrls): void
    {
        $insertData = [];
        
        foreach ($nonIndexableUrls as $urlData) {
            $url = $this->extractRelativeUrl($urlData['Address'] ?? '');
            $canonicalUrl = $this->extractRelativeUrl($urlData['Canonical Page'] ?? '');
            
            if ($url) {
                $insertData[] = [
                    'url' => $url,
                    'url_type' => 'non_indexable',
                    'status_code' => $urlData['Status Code'] ?? null,
                    'indexability' => $urlData['Indexability'] ?? null,
                    'canonical_url' => $canonicalUrl
                ];
            }
        }

        if (!empty($insertData)) {
            $connection->insertMultiple($urlTable, $insertData);
            $this->logger->info('Imported ' . count($insertData) . ' non-indexable URLs');
        }
    }

    /**
     * Parse CSV file and return array of data
     *
     * @param string $filePath
     * @return array
     */
    private function parseCsvFile(string $filePath): array
    {
        $data = [];
        try {
            $content = $this->fileDriver->fileGetContents($filePath);
            $lines = explode("\n", $content);
            $headers = str_getcsv(array_shift($lines));

            foreach ($lines as $line) {
                if (trim($line)) {
                    $row = str_getcsv($line);
                    if (count($row) >= count($headers)) {
                        $data[] = array_combine($headers, $row);
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->error("Error parsing CSV file {$filePath}: " . $e->getMessage());
        }

        return $data;
    }

    /**
     * Check if URL data indicates it's indexable
     *
     * @param array $urlData
     * @return bool
     */
    private function isIndexable(array $urlData): bool
    {
        $indexability = $urlData['Indexability'] ?? '';
        $statusCode = $urlData['Status Code'] ?? '';
        
        return $indexability === 'Indexable' && $statusCode === '200';
    }

    /**
     * Extract relative URL from full URL
     *
     * @param string $fullUrl
     * @return string|null
     */
    private function extractRelativeUrl(string $fullUrl): ?string
    {
        if (empty($fullUrl)) {
            return null;
        }

        $parsedUrl = parse_url($fullUrl);
        if (!$parsedUrl || !isset($parsedUrl['path'])) {
            return null;
        }

        return $parsedUrl['path'];
    }

    /**
     * {@inheritdoc}
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public static function getVersion()
    {
        return '1.0.2';
    }
}
