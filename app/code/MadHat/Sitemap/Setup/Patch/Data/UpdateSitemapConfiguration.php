<?php
/**
 * Copyright © MadHat, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace MadHat\Sitemap\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchVersionInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Psr\Log\LoggerInterface;

/**
 * Class UpdateSitemapConfiguration
 *
 * This patch updates sitemap configuration:
 * 1. Deletes B2B sitemap (sitemapb2c.xml)
 * 2. Renames B2C sitemap from sitemapb2b.xml to sitemap.xml
 */
class UpdateSitemapConfiguration implements DataPatchInterface, PatchVersionInterface
{
    private ModuleDataSetupInterface $moduleDataSetup;
    private LoggerInterface $logger;

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param LoggerInterface $logger
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        LoggerInterface $logger
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->logger = $logger;
    }

    /**
     * {@inheritdoc}
     */
    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        try {
            $connection = $this->moduleDataSetup->getConnection();
            $sitemapTable = $this->moduleDataSetup->getTable('sitemap');

            $this->logger->info('UpdateSitemapConfiguration patch v1.0.4 starting execution');

            // Log current sitemap configuration
            $currentSitemaps = $connection->fetchAll(
                $connection->select()->from($sitemapTable)
            );

            $this->logger->info('Current sitemap configuration before update:', $currentSitemaps);

            // Also log to system for debugging
            foreach ($currentSitemaps as $sitemap) {
                $this->logger->info("Found sitemap: ID={$sitemap['sitemap_id']}, File={$sitemap['sitemap_filename']}, Store={$sitemap['store_id']}");
            }

            // Remove B2B sitemap (sitemapb2c.xml which is actually for B2B pages)
            $deletedRows = $connection->delete(
                $sitemapTable,
                ['sitemap_filename = ?' => 'sitemapb2c.xml']
            );

            if ($deletedRows > 0) {
                $this->logger->info("Successfully deleted B2B sitemap (sitemapb2c.xml). Deleted {$deletedRows} row(s).");
            } else {
                $this->logger->info('No B2B sitemap (sitemapb2c.xml) found to delete.');
            }

            // Rename remaining sitemap from sitemapb2b.xml to sitemap.xml
            $updatedRows = $connection->update(
                $sitemapTable,
                ['sitemap_filename' => 'sitemap.xml'],
                ['sitemap_filename = ?' => 'sitemapb2b.xml']
            );

            if ($updatedRows > 0) {
                $this->logger->info("Successfully renamed sitemap from sitemapb2b.xml to sitemap.xml. Updated {$updatedRows} row(s).");
            } else {
                $this->logger->info('No sitemap named sitemapb2b.xml found to rename.');
            }

            // Log final sitemap configuration
            $finalSitemaps = $connection->fetchAll(
                $connection->select()->from($sitemapTable)
            );

            $this->logger->info('Final sitemap configuration after update:', $finalSitemaps);

            // Also log final state for debugging
            foreach ($finalSitemaps as $sitemap) {
                $this->logger->info("Final sitemap: ID={$sitemap['sitemap_id']}, File={$sitemap['sitemap_filename']}, Store={$sitemap['store_id']}");
            }

            $this->logger->info('UpdateSitemapConfiguration patch v1.0.4 completed successfully');

        } catch (\Exception $e) {
            $this->logger->error('Error in UpdateSitemapConfiguration patch v1.0.4: ' . $e->getMessage());
            throw $e;
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * {@inheritdoc}
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public static function getVersion()
    {
        return '1.0.4';
    }
}
