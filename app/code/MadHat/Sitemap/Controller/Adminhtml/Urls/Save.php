<?php
/**
 * Copyright © MadHat, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace MadHat\Sitemap\Controller\Adminhtml\Urls;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use MadHat\Sitemap\Model\SitemapUrlFactory;
use MadHat\Sitemap\Model\ResourceModel\SitemapUrl as SitemapUrlResource;

/**
 * Class Save
 * 
 * Controller for saving sitemap URL
 */
class Save extends Action
{
    /**
     * Authorization level of a basic admin session
     */
    const ADMIN_RESOURCE = 'MadHat_Sitemap::sitemap_urls';

    /**
     * @var SitemapUrlFactory
     */
    protected $sitemapUrlFactory;

    /**
     * @var SitemapUrlResource
     */
    protected $sitemapUrlResource;

    /**
     * @param Context $context
     * @param SitemapUrlFactory $sitemapUrlFactory
     * @param SitemapUrlResource $sitemapUrlResource
     */
    public function __construct(
        Context $context,
        SitemapUrlFactory $sitemapUrlFactory,
        SitemapUrlResource $sitemapUrlResource
    ) {
        parent::__construct($context);
        $this->sitemapUrlFactory = $sitemapUrlFactory;
        $this->sitemapUrlResource = $sitemapUrlResource;
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $data = $this->getRequest()->getPostValue();

        if ($data) {
            $id = $this->getRequest()->getParam('id');
            $model = $this->sitemapUrlFactory->create();

            if ($id) {
                $this->sitemapUrlResource->load($model, $id);
                if (!$model->getId()) {
                    $this->messageManager->addErrorMessage(__('This URL no longer exists.'));
                    return $resultRedirect->setPath('*/*/');
                }
            }

            $model->setData($data);

            try {
                $this->sitemapUrlResource->save($model);
                $this->messageManager->addSuccessMessage(__('You saved the sitemap URL.'));
                
                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit', ['id' => $model->getId()]);
                }
                
                return $resultRedirect->setPath('*/*/');
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
                return $resultRedirect->setPath('*/*/edit', ['id' => $id]);
            }
        }

        return $resultRedirect->setPath('*/*/');
    }
}
